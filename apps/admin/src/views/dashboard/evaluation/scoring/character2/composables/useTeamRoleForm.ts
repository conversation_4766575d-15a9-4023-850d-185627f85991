/**
 * 团队角色表单逻辑组合式函数
 */

import { ref, watch, nextTick } from 'vue';
import type { TeamRoleWeightItem, TeamRoleNormItem, TableColumn } from '../type';
import { CHARACTER2_CONSTANTS } from '../type';
import { validateTeamRoleWeightSum } from '../api';

export function useTeamRoleForm() {
    // 权重表数据 - 固定九个角色
    const weightList = ref<TeamRoleWeightItem[]>(
        CHARACTER2_CONSTANTS.DEFAULT_TEAM_ROLES.map((name, index) => ({
            id: (index + 1).toString(),
            name,
            weight: 0,
        }))
    );

    // 常模数据 - 对应九个角色
    const normList = ref<TeamRoleNormItem[]>(
        CHARACTER2_CONSTANTS.DEFAULT_TEAM_ROLES.map((name, index) => ({
            id: (index + 1).toString(),
            name,
            avgScore: 0,
            stdDev: 0,
        }))
    );

    // 权重表列配置
    const weightColumns: TableColumn[] = [
        {
            label: '团队角色名称',
            field: 'name',
            width: 200,
        },
        {
            label: '权重',
            field: 'weight',
            width: 150,
        },
    ];

    // 常模表列配置
    const normColumns: TableColumn[] = [
        {
            label: '团队角色名称',
            field: 'name',
            width: 200,
        },
        {
            label: '常模平均分',
            field: 'avgScore',
            width: 150,
        },
        {
            label: '常模标准差',
            field: 'stdDev',
            width: 150,
        },
    ];

    // 权重和验证
    const weightSumError = ref(false);

    /**
     * 验证权重和
     */
    function validateWeightSum() {
        weightSumError.value = !validateTeamRoleWeightSum(weightList.value);
    }

    /**
     * 从API数据初始化内部状态
     */
    function initFromApiData(apiData: any[]) {
        try {
            if (Array.isArray(apiData) && apiData.length > 0) {
                const firstItem = apiData[0];
                if (firstItem?.rowDataList && firstItem.rowDataList.length === 9) {
                    weightList.value = firstItem.rowDataList.map((row: any, index: number) => ({
                        id: row.encDimensionId || (index + 1).toString(),
                        name: row.dimensionName || CHARACTER2_CONSTANTS.DEFAULT_TEAM_ROLES[index],
                        weight: row.weight || 0,
                    }));

                    normList.value = firstItem.rowDataList.map((row: any, index: number) => ({
                        id: row.encDimensionId || (index + 1).toString(),
                        name: row.dimensionName || CHARACTER2_CONSTANTS.DEFAULT_TEAM_ROLES[index],
                        avgScore: row.normalAverageScore || 0,
                        stdDev: row.normalStandardDeviation || 0,
                    }));
                }
            }
        } catch (error) {
            console.error('团队角色从API数据初始化失败:', error);
        }
    }

    /**
     * 获取当前表单数据
     */
    function getCurrentFormData() {
        return {
            weightList: weightList.value,
            normList: normList.value,
        };
    }

    /**
     * 重置表单数据
     */
    function resetFormData() {
        weightList.value = CHARACTER2_CONSTANTS.DEFAULT_TEAM_ROLES.map((name, index) => ({
            id: (index + 1).toString(),
            name,
            weight: 0,
        }));

        normList.value = CHARACTER2_CONSTANTS.DEFAULT_TEAM_ROLES.map((name, index) => ({
            id: (index + 1).toString(),
            name,
            avgScore: 0,
            stdDev: 0,
        }));

        weightSumError.value = false;
    }

    // 监听权重表名称变化，同步到常模表
    watch(
        () => weightList.value.map((item) => ({ id: item.id, name: item.name })),
        (newNames) => {
            newNames.forEach((item, index) => {
                if (normList.value[index]) {
                    normList.value[index].name = item.name;
                }
            });
        },
        { deep: true }
    );

    return {
        // 状态
        weightList,
        normList,
        weightColumns,
        normColumns,
        weightSumError,

        // 方法
        validateWeightSum,
        initFromApiData,
        getCurrentFormData,
        resetFormData,
    };
}
