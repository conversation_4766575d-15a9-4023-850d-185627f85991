/**
 * 五维性格测评 2.0 专用 API 服务
 */

import { get, post } from '@/services/http';
import type { ApiResponse, Character2DetailData, SaveParams, DimensionItem } from './type';
import { CHARACTER2_CONSTANTS } from './type';

/**
 * 获取维度查询数据
 */
export async function getDimensionQuery(params: { productId?: number; dimensionLevel?: number; flat?: boolean }): Promise<ApiResponse<DimensionItem[]>> {
    try {
        const response = await get('/wapi/admin/evaluation/dimension/query.json', {
            productId: CHARACTER2_CONSTANTS.PRODUCT_ID,
            dimensionLevel: CHARACTER2_CONSTANTS.DIMENSION_LEVEL,
            flat: true,
            ...params,
        });

        return {
            code: response.code,
            message: response.message,
            data: response.data || [],
        };
    } catch (error) {
        throw new Error('获取维度数据失败');
    }
}

/**
 * 获取计分参数详情
 */
export async function getScoreParamDetail(params: { productId: string | number }): Promise<ApiResponse<Character2DetailData>> {
    try {
        const response = await get('/wapi/admin/evaluation/score/param/detail.json', params);

        // 数据转换和验证
        const data = response.data ? transformDetailData(response.data) : undefined;

        return {
            code: response.code,
            message: response.message,
            data,
        };
    } catch (error) {
        throw new Error('获取计分参数详情失败');
    }
}

/**
 * 保存计分参数
 */
export async function saveScoreParam(params: SaveParams): Promise<ApiResponse> {
    try {
        // 参数验证
        validateSaveParams(params);

        // 数据转换
        const transformedParams = transformSaveParams(params);

        const response = await post('/wapi/admin/evaluation/score/param/save.json', transformedParams);

        return {
            code: response.code,
            message: response.message,
            data: response.data,
        };
    } catch (error) {
        console.error('保存计分参数失败:', error);
        throw error;
    }
}

/**
 * 转换详情数据
 */
function transformDetailData(rawData: any): Character2DetailData {
    const { paramA, paramB, paramC, fileParam } = rawData;

    return {
        paramA: Number(paramA) || 0,
        paramB: Number(paramB) || 0,
        paramC: {
            scoreMin: Number(paramC?.scoreMin) || 0,
            scoreMax: Number(paramC?.scoreMax) || 0,
            levelArray: Array.isArray(paramC?.levelArray) ? paramC.levelArray.map(Number) : [],
            keyPotentialQualityList: Array.isArray(paramC?.keyPotentialQualityList) ? paramC.keyPotentialQualityList.map(transformConfigItem) : [],
            positionQualityModelMatchList: Array.isArray(paramC?.positionQualityModelMatchList) ? paramC.positionQualityModelMatchList.map(transformConfigItem) : [],
            teamRoleList: Array.isArray(paramC?.teamRoleList) ? paramC.teamRoleList : [],
        },
        fileParam: fileParam || {},
    };
}

/**
 * 转换配置项数据
 */
function transformConfigItem(item: any) {
    return {
        encId: item.encId || '',
        headName: item.headName || '',
        rowDataList: Array.isArray(item.rowDataList)
            ? item.rowDataList.map((row: any) => ({
                  encDimensionId: row.encDimensionId || '',
                  dimensionName: row.dimensionName || '',
                  define: row.define || null,
                  weight: Number(row.weight) || 0,
                  normalAverageScore: Number(row.normalAverageScore) || 0,
                  normalStandardDeviation: Number(row.normalStandardDeviation) || 0,
              }))
            : [],
    };
}

/**
 * 转换保存参数
 */
function transformSaveParams(params: SaveParams) {
    return {
        productId: params.productId,
        paramA: Number(params.paramA),
        paramB: Number(params.paramB),
        paramC: {
            scoreMin: Number(params.paramC.scoreMin),
            scoreMax: Number(params.paramC.scoreMax),
            levelArray: params.paramC.levelArray.map(Number),
            keyPotentialQualityList: params.paramC.keyPotentialQualityList,
            positionQualityModelMatchList: params.paramC.positionQualityModelMatchList,
            teamRoleList: params.paramC.teamRoleList,
        },
    };
}

/**
 * 验证保存参数
 */
function validateSaveParams(params: SaveParams): void {
    const { paramA, paramB, paramC } = params;

    // 基础参数验证
    if (!isValidParam(paramA) || !isValidParam(paramB)) {
        throw new Error('参数 A 和参数 B 必须在 1-99 范围内');
    }

    // 分数范围验证
    if (!isValidParam(paramC.scoreMin) || !isValidParam(paramC.scoreMax)) {
        throw new Error('得分上下限必须在 1-99 范围内');
    }

    if (paramC.scoreMin >= paramC.scoreMax) {
        throw new Error('得分下限必须小于得分上限');
    }

    // 等级数组验证
    if (!Array.isArray(paramC.levelArray) || paramC.levelArray.length === 0) {
        throw new Error('等级划分数据不能为空');
    }

    // 验证等级数组的递增性
    for (let i = 0; i < paramC.levelArray.length - 1; i++) {
        if (paramC.levelArray[i] >= paramC.levelArray[i + 1]) {
            throw new Error('等级划分数值必须递增');
        }
    }
}

/**
 * 验证参数是否在有效范围内
 */
function isValidParam(value: number): boolean {
    return typeof value === 'number' && value >= CHARACTER2_CONSTANTS.PARAM_MIN && value <= CHARACTER2_CONSTANTS.PARAM_MAX;
}

/**
 * 构建关键潜在素质列表数据
 */
export function buildKeyPotentialQualityList(formData: { configGroups: any[]; weightTableData: any[]; normTableData: any[] }) {
    const { configGroups, weightTableData, normTableData } = formData;

    return configGroups.map((group) => ({
        encId: group.id || '',
        headName: group.name || '关键潜在素质',
        rowDataList: weightTableData.map((row) => ({
            encDimensionId: row.encryptDimensionId || '',
            dimensionName: row.dimensionName || '',
            define: null,
            weight: row[`weight_${group.id}`] || 0,
            normalAverageScore: normTableData.find((norm) => norm.groupId === group.id)?.avgScore || 0,
            normalStandardDeviation: normTableData.find((norm) => norm.groupId === group.id)?.stdDev || 0,
        })),
    }));
}

/**
 * 构建岗位素质模型匹配列表数据
 */
export function buildPositionQualityModelMatchList(formData: { configGroups: any[]; weightTableData: any[]; normTableData: any[] }) {
    const { configGroups, weightTableData, normTableData } = formData;

    return configGroups.map((group) => ({
        encId: group.id || '',
        headName: group.name || '岗位素质模型',
        rowDataList: weightTableData.map((row) => ({
            encDimensionId: row.encryptDimensionId || '',
            dimensionName: row.dimensionName || '',
            define: null,
            weight: row[`weight_${group.id}`] || 0,
            normalAverageScore: normTableData.find((norm) => norm.groupId === group.id)?.avgScore || 0,
            normalStandardDeviation: normTableData.find((norm) => norm.groupId === group.id)?.stdDev || 0,
        })),
    }));
}

/**
 * 构建团队角色列表数据
 */
export function buildTeamRoleList(formData: { weightList: any[]; normList: any[] }) {
    const { weightList, normList } = formData;

    return [
        {
            encId: '',
            headName: '团队角色',
            rowDataList: weightList.map((item, index) => ({
                encDimensionId: item.id || '',
                dimensionName: item.name || '',
                define: null,
                weight: item.weight || 0,
                normalAverageScore: normList[index]?.avgScore || 0,
                normalStandardDeviation: normList[index]?.stdDev || 0,
            })),
        },
    ];
}

/**
 * 验证权重和是否等于1
 */
export function validateWeightSum(configGroups: any[], weightTableData: any[]): { hasError: boolean; errorGroups: string[] } {
    const errorGroups: string[] = [];

    configGroups.forEach((group) => {
        const sum = weightTableData.reduce((acc, row) => acc + (row[`weight_${group.id}`] || 0), 0);

        if (Math.abs(sum - 1) > 0.0001) {
            errorGroups.push(group.name);
        }
    });

    return {
        hasError: errorGroups.length > 0,
        errorGroups,
    };
}

/**
 * 验证团队角色权重和
 */
export function validateTeamRoleWeightSum(weightList: any[]): boolean {
    const sum = weightList.reduce((acc, item) => acc + (item.weight || 0), 0);
    return Math.abs(sum - 1) <= 0.0001;
}

/**
 * 错误处理工具函数
 */
export function handleApiError(error: any): never {
    if (error?.response?.data?.message) {
        throw new Error(error.response.data.message);
    } else if (error?.message) {
        throw new Error(error.message);
    } else {
        throw new Error('操作失败，请稍后重试');
    }
}
